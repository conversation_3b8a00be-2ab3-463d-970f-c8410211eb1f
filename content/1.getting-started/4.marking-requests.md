---
title: Making requests
description: Make your first request to the GeminiGen.AI API and get started with the powerful AI generation features.
---

After setting up your account and obtaining your API key, setup webhooks, you can start making requests to the GeminiGen.AI API. This guide will walk you through the process of making your first request and provide examples of how to use the API to generate speech from text, create images, videos, and more.

::code-group

```bash [terminal]
curl -X POST https://api.geminigen.ai/uapi/v1/text-to-speech \
  -H "Content-Type: application/json" \
  -H "x-api-key: <your api key>" \
  -d '{
    "model": "tts-1",
    "voice_id": "OA001",
    "speed": 1,
    "input": "Hello, my name is GeminiGen.AI. I am a text-to-speech model."
  }' \
```

```ts [py]
import requests

url = "https://api.geminigen.ai/uapi/v1/text-to-speech"
headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
}
data = {
    "model": "tts-1",
    "voice_id": "OA001",
    "speed": 1,
    "input": "Hello world!"
}

response = requests.post(url, headers=headers, json=data)
print(response.json())
```

```ts [ts]
import axios from 'axios';

const url = "https://api.geminigen.ai/uapi/v1/text-to-speech";
const headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
};
const data = {
    "model": "tts-1",
    "voice_id": "OA001",
    "speed": 1,
    "input": "Hello world!"
};

axios.post(url, data, { headers })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```

::

:::callout
---
icon: i-heroicons-light-bulb
target: _blank
to: https://geminigen.ai/app/speech-gen
---
You can find the voice IDs in Voice Library page.
