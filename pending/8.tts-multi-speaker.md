---
title: Text-to-Speech Multi Speaker
description: Convert text to speech with multiple speakers and advanced voice control options.
---

The GeminiGen.AI Multi Speaker Text-to-Speech API allows you to convert text into high-quality, natural-sounding speech with support for multiple speakers and advanced voice customization. You can use this API to create engaging audio content with different voices, generate complex narrations, or create interactive voice experiences.

## Text To Speech Multi Speaker
`POST https://api.geminigen.ai/uapi/v1/tts-multi-speaker`

This endpoint allows you to convert text into speech with multiple speaker support and advanced voice control options. You can customize voices, emotions, speed, and output formats for sophisticated audio generation.

### Example Request
::code-group
```bash [terminal]
curl -X POST https://api.geminigen.ai/uapi/v1/tts-multi-speaker \
  -H "Content-Type: application/json" \
  -H "x-api-key: <your api key>" \
  -d '{
    "model": "tts-flash",
    "speed": 1,
    "blocks": [
      {
        "input": "Hello, welcome to our multi-speaker demonstration."
      }
    ],
    "output_format": "mp3",
    "output_channel": "mono",
    "voice": "OA001",
    "name": "Multi Speaker Demo"
  }'
```

```python [py]
import requests

url = "https://api.geminigen.ai/uapi/v1/tts-multi-speaker"
headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
}
data = {
    "model": "tts-flash",
    "speed": 1,
    "blocks": [
        {
            "input": "Hello, welcome to our multi-speaker demonstration."
        }
    ],
    "output_format": "mp3",
    "output_channel": "mono",
    "voice": "OA001",
    "name": "Multi Speaker Demo"
}

response = requests.post(url, headers=headers, json=data)
print(response.json())
```

```javascript [js]
import axios from 'axios';

const url = "https://api.geminigen.ai/uapi/v1/tts-multi-speaker";
const headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
};

const data = {
    model: "tts-flash",
    speed: 1,
    blocks: [
        {
            input: "Hello, welcome to our multi-speaker demonstration."
        }
    ],
    output_format: "mp3",
    output_channel: "mono",
    voice: "OA001",
    name: "Multi Speaker Demo"
};

axios.post(url, data, { headers })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```
::

### Query Parameters

`ai_model` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The AI model to use for text-to-speech generation. Default value is `tts-flash`.

### Request Body Parameters

`voices` [array]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Array of voice configurations for multi-speaker scenarios. Each voice can have different settings and characteristics.

`model_name` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The name of the model to use for speech generation.

`model` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The model identifier for text-to-speech generation. Default is `tts-flash`.

`speed` [number]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The speed of speech generation. Default is `1` (normal speed). Range typically from 0.5 (slower) to 2.0 (faster).

`blocks` [array]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Array of text blocks to convert to speech. Each block contains:
- `input` [string] - The text content to convert to speech

`output_format` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The audio output format. Supported options:
- `mp3` - MP3 audio format (default)
- `wav` - WAV audio format

`emotion` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The emotional tone to apply to the speech. Options may include:
- `neutral` - Neutral tone
- `happy` - Happy/cheerful tone
- `sad` - Sad/melancholic tone
- `angry` - Angry/aggressive tone
- `excited` - Excited/energetic tone

`custom_prompt` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Custom prompt or instructions for voice generation to guide the speech characteristics.

`output_channel` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Audio channel configuration:
- `mono` - Single channel audio (default)
- `stereo` - Dual channel audio

`voice` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The voice ID to use for speech generation. Examples include:
- `OA001` - Default voice option
- Other voice IDs as available in the system

`input_file_path` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Path to an input file if using file-based text input instead of direct text.

`input` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Direct text input for speech generation (alternative to using blocks).

`name` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

A descriptive name for the generated audio file or request.

### Example Response
```json [Response]
{
  "id": 12345,
  "uuid": "tts_multi_abc123def456",
  "user_id": 789,
  "model_name": "tts-flash",
  "input_text": "Hello, welcome to our multi-speaker demonstration.",
  "generate_result": "https://cdn.geminigen.ai/audio/tts_multi_abc123def456.mp3",
  "type": "tts_multi_speaker",
  "used_credit": 5,
  "status": 2,
  "status_desc": "completed",
  "status_percentage": 100,
  "error_code": "",
  "error_message": "",
  "created_at": "2025-07-21T13:26:53.076Z",
  "updated_at": "2025-07-21T13:26:53.076Z",
  "file_size": 1048576,
  "expired_at": "2025-08-21T13:26:53.076Z",
  "name": "Multi Speaker Demo",
  "created_by": "user_789",
  "output_format": "mp3",
  "output_channel": "mono",
  "duration": 3.5
}
```

### Response Fields

`id` - Unique identifier for the generation request

`uuid` - Universal unique identifier for the generated audio

`generate_result` - URL to the generated audio file

`status` - Generation status (1: processing, 2: completed, 3: failed)

`status_percentage` - Progress percentage (0-100)

`used_credit` - Credits consumed for this generation

`file_size` - Size of the generated audio file in bytes

`output_format` - Format of the generated audio file

`output_channel` - Channel configuration of the audio

`duration` - Duration of the generated audio in seconds

### Status Codes

- `1` - Processing: The audio is being generated
- `2` - Completed: Audio generation successful
- `3` - Failed: Generation failed (check error_message)

### Best Practices

1. **Multi-Speaker Setup**: Use the `voices` array to configure different speakers for complex narratives
2. **Block Organization**: Structure your content using `blocks` for better control over speech segments
3. **Speed Control**: Adjust `speed` based on content type (slower for educational, faster for casual)
4. **Format Selection**: Choose appropriate `output_format` based on your use case and quality requirements
5. **Emotion Consistency**: Use consistent `emotion` settings for coherent audio experiences

### Common Use Cases

- **Audiobooks**: Multi-character narrations with different voices
- **Podcasts**: Multiple host conversations and interviews
- **Educational Content**: Teacher-student dialogues and interactive lessons
- **Marketing**: Product demonstrations with multiple speakers
- **Entertainment**: Audio dramas and storytelling with character voices

### Tips for Better Results

1. **Voice Variety**: Use different voices in the `voices` array for distinct characters
2. **Emotional Context**: Apply appropriate `emotion` settings for different content types
3. **Pacing Control**: Adjust `speed` for different speakers or content sections
4. **Quality Settings**: Choose higher quality formats for professional content
5. **Content Structure**: Organize text into logical `blocks` for better speech flow
